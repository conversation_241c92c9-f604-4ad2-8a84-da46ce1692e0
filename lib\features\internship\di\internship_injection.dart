/// -----
/// internship_injection.dart
/// 
/// 实习模块依赖注入配置
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:get_it/get_it.dart';
import '../../../core/network/dio_client.dart';
import '../../../core/network/network_info.dart';
import '../data/datasources/remote/internship_plan_remote_data_source.dart';
import '../data/datasources/remote/internship_plan_remote_data_source_impl.dart';
import '../data/repositories/internship_plan_repository_impl.dart';
import '../domain/repositories/internship_plan_repository.dart';
import '../domain/usecases/get_teacher_internship_plans_usecase.dart';
import '../domain/usecases/get_internship_plan_detail_usecase.dart';
import '../presentation/bloc/teacher_plan_list/teacher_plan_list_bloc.dart';
import '../presentation/bloc/plan_detail/plan_detail_bloc.dart';

/// 全局依赖注入容器
final getIt = GetIt.instance;

/// 初始化实习模块依赖
/// 
/// 注册实习模块的数据源、仓库、用例和BLoC
Future<void> setupInternshipDependencies() async {
  // 数据源
  getIt.registerLazySingleton<InternshipPlanRemoteDataSource>(
    () => InternshipPlanRemoteDataSourceImpl(getIt<DioClient>()),
  );

  // 仓库
  getIt.registerLazySingleton<InternshipPlanRepository>(
    () => InternshipPlanRepositoryImpl(
      getIt<InternshipPlanRemoteDataSource>(),
      getIt<NetworkInfo>(),
    ),
  );

  // 用例
  getIt.registerFactory<GetTeacherInternshipPlansUseCase>(
    () => GetTeacherInternshipPlansUseCase(getIt<InternshipPlanRepository>()),
  );

  getIt.registerFactory<GetInternshipPlanDetailUseCase>(
    () => GetInternshipPlanDetailUseCase(getIt<InternshipPlanRepository>()),
  );

  // BLoC
  getIt.registerFactory<TeacherPlanListBloc>(
    () => TeacherPlanListBloc(
      getTeacherInternshipPlansUseCase: getIt<GetTeacherInternshipPlansUseCase>(),
    ),
  );

  getIt.registerFactory<PlanDetailBloc>(
    () => PlanDetailBloc(getIt<GetInternshipPlanDetailUseCase>()),
  );
}

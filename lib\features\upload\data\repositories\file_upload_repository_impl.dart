/// -----
/// file_upload_repository_impl.dart
/// 
/// 文件上传仓库实现类，实现文件上传相关的数据操作
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions/network_exception.dart';
import '../../../../core/error/exceptions/server_exception.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/file_requirement.dart';
import '../../domain/repositories/file_upload_repository.dart';
import '../datasources/remote/file_upload_remote_data_source.dart';

/// 文件上传仓库实现类
/// 
/// 实现文件上传相关的数据操作
class FileUploadRepositoryImpl implements FileUploadRepository {
  final FileUploadRemoteDataSource _remoteDataSource;
  final NetworkInfo _networkInfo;

  FileUploadRepositoryImpl(
    this._remoteDataSource,
    this._networkInfo,
  );

  @override
  Future<Either<Failure, List<FileRequirement>>> getFileRequirements({
    required String planId,
  }) async {
    if (await _networkInfo.isConnected) {
      try {
        final fileRequirementModels = await _remoteDataSource.getFileRequirements(
          planId: planId,
        );
        
        // 将模型转换为实体
        final fileRequirements = fileRequirementModels
            .map((model) => model.toEntity())
            .toList();
            
        return Right(fileRequirements);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(e.message));
      } catch (e) {
        return Left(ServerFailure('获取文件要求列表失败: $e'));
      }
    } else {
      return Left(NetworkFailure('网络连接失败，请检查网络设置'));
    }
  }
}

import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/services/auth_expiry_service.dart';
import 'package:flutter_demo/core/storage/local_storage.dart';
import 'package:flutter_demo/core/utils/logger.dart';

/// 认证拦截器
///
/// 处理token的添加、刷新和过期逻辑
class AuthInterceptor extends Interceptor {
  /// 日志标签
  static const String _tag = 'AuthInterceptor';

  /// 本地存储实例
  final LocalStorage _localStorage;

  /// 构造函数
  AuthInterceptor(this._localStorage);

  @override
  Future<void> onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // 获取本地存储的token
    final token = _localStorage.getString(AppConstants.tokenKey);

    // 如果token存在，添加到请求头
    if (token != null && token.isNotEmpty) {
      options.headers['token'] = 'Bearer $token';

      // 使用Logger记录认证信息（脱敏处理）
      final maskedToken = Logger.maskSensitiveInfo(token);
      Logger.debug(_tag, '添加认证令牌: Bearer $maskedToken');
    }

    super.onRequest(options, handler);
  }

  @override
  Future<void> onError(DioException err, ErrorInterceptorHandler handler) async {
    Logger.debug(_tag, '拦截器收到错误: ${err.response?.statusCode}, 类型: ${err.type}');

    // 处理401未授权错误
    if (err.response?.statusCode == 401) {
      Logger.warning(_tag, '检测到401未授权错误，开始处理认证失效');

      try {
        // 使用依赖注入容器中的AuthExpiryService实例
        final authExpiryService = GetIt.instance<AuthExpiryService>();

        // 直接处理认证失效，清除数据并跳转登录页面
        await authExpiryService.handleAuthExpiryWithoutContext(
          message: '登录已过期，请重新登录',
        );

        Logger.info(_tag, '认证失效处理完成');

        // 继续抛出错误，让上层知道请求失败了
        return handler.next(err);
      } on Exception catch (e, s) {
        Logger.error(
          _tag,
          '处理认证失效时发生错误',
          exception: e,
          stackTrace: s,
        );
        // 即使处理失败，也要继续抛出原始错误
        return handler.next(err);
      }
    }

    // 其他错误直接传递
    return handler.next(err);
  }

}

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_demo/core/network/interceptors/auth_interceptor.dart';
import 'package:flutter_demo/core/network/interceptors/error_interceptor.dart';
import 'package:flutter_demo/core/network/interceptors/logging_interceptor.dart';
import 'package:flutter_demo/core/network/interceptors/cache_interceptor.dart';
import 'package:flutter_demo/core/network/interceptors/response_interceptor.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/config/env_config.dart';
import 'package:flutter_demo/core/storage/local_storage.dart';
import 'package:flutter_demo/core/error/exceptions/server_exception.dart';
import 'package:flutter_demo/core/utils/logger.dart';

/// Dio HTTP客户端封装
///
/// 提供统一的网络请求接口，支持环境配置、拦截器和缓存
class DioClient {
  // 单例模式
  static final DioClient _instance = DioClient._internal();

  factory DioClient() {
    return _instance;
  }

  DioClient._internal() {
    // 默认使用AppConstants中的配置
    _envConfig = const EnvConfig(
      envType: EnvType.dev,
      baseUrl: AppConstants.baseUrl,
    );
    _initDio();
  }

  late Dio _dio;

  /// 当前环境配置
  late EnvConfig _envConfig;

  /// 获取Dio实例
  Dio get dio => _dio;

  /// 获取当前环境配置
  EnvConfig get envConfig => _envConfig;

  /// 使用指定环境配置初始化
  ///
  /// 可在应用启动时调用，以设置当前环境
  void initWithConfig(EnvConfig config) {
    _envConfig = config;
    _initDio();
    debugPrint('DioClient已使用环境配置初始化: ${config.name}');
  }

  /// 初始化Dio实例
  void _initDio() {
    final baseOptions = BaseOptions(
      baseUrl: _envConfig.baseUrl,
      connectTimeout: Duration(milliseconds: _envConfig.timeoutMillis),
      receiveTimeout: Duration(milliseconds: _envConfig.timeoutMillis),
      sendTimeout: Duration(milliseconds: _envConfig.timeoutMillis),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      // 确保在所有模式下都允许HTTP请求
      validateStatus: (status) => status != null && status < 500,
    );

    _dio = Dio(baseOptions);

    // 添加拦截器（注意顺序很重要）
    _dio.interceptors.add(LoggingInterceptor());
    // 添加响应拦截器，处理业务状态码
    _dio.interceptors.add(ResponseInterceptor());
    // 注意：AuthInterceptor 将在 addAuthInterceptor() 方法中添加
    // ErrorInterceptor 必须在 AuthInterceptor 之后添加，这样 AuthInterceptor 可以先处理401错误
    _dio.interceptors.add(ErrorInterceptor());

    // 如果环境配置启用了缓存，添加缓存拦截器
    if (_envConfig.enableCache) {
      addCacheInterceptor(['config', 'categories'],
        maxAge: _envConfig.cacheMaxAge);
    }
  }

  /// 添加认证拦截器
  ///
  /// 用于处理token的添加、刷新和过期逻辑
  /// AuthInterceptor 必须在 ErrorInterceptor 之前执行，以便正确处理401错误
  void addAuthInterceptor(LocalStorage localStorage) {
    // 检查是否已经添加了 AuthInterceptor
    bool hasAuthInterceptor = _dio.interceptors.any((interceptor) => interceptor is AuthInterceptor);
    if (hasAuthInterceptor) {
      Logger.warning('DioClient', 'AuthInterceptor 已经存在，跳过添加');
      return;
    }

    // 找到 ErrorInterceptor 的位置
    int errorInterceptorIndex = -1;
    for (int i = 0; i < _dio.interceptors.length; i++) {
      if (_dio.interceptors[i] is ErrorInterceptor) {
        errorInterceptorIndex = i;
        break;
      }
    }

    // 如果找到了 ErrorInterceptor，在它之前插入 AuthInterceptor
    if (errorInterceptorIndex != -1) {
      _dio.interceptors.insert(errorInterceptorIndex, AuthInterceptor(localStorage));
      Logger.info('DioClient', 'AuthInterceptor 已添加到 ErrorInterceptor 之前，位置: $errorInterceptorIndex');
    } else {
      // 如果没找到 ErrorInterceptor，直接添加到末尾
      _dio.interceptors.add(AuthInterceptor(localStorage));
      Logger.info('DioClient', 'AuthInterceptor 已添加到拦截器列表末尾');
    }

    // 打印当前拦截器列表
    _printInterceptors();
  }

  /// 打印当前拦截器列表（调试用）
  void _printInterceptors() {
    Logger.debug('DioClient', '当前拦截器列表:');
    for (int i = 0; i < _dio.interceptors.length; i++) {
      final interceptor = _dio.interceptors[i];
      Logger.debug('DioClient', '  [$i] ${interceptor.runtimeType}');
    }
  }

  /// 获取Dio实例（仅用于调试）
  Dio get dioInstance => _dio;

  /// 添加缓存拦截器
  ///
  /// [cacheablePaths] - 可以缓存的API路径列表，例如 ['/config', '/categories']
  /// [maxAge] - 缓存最大保存时间
  /// [forceCache] - 是否强制使用缓存，即使缓存已过期
  void addCacheInterceptor(List<String> cacheablePaths, {
    Duration maxAge = const Duration(hours: 1),
    bool forceCache = false,
  }) {
    _dio.interceptors.add(CacheInterceptor(
      cacheablePaths: cacheablePaths,
      maxAge: maxAge,
      forceCache: forceCache,
    ));
  }

  /// 设置授权令牌
  ///
  /// 在请求头中添加token
  void setToken(String token) {
    _dio.options.headers['token'] = 'Bearer $token';
  }

  /// 处理URL路径，避免重复斜杠
  String _normalizePath(String path) {
    // 如果baseUrl以/结尾，且path以/开头，则移除path中的/
    if (_envConfig.baseUrl.endsWith('/') && path.startsWith('/')) {
      return path.substring(1);
    }
    // 如果baseUrl不以/结尾，且path不以/开头，则添加/
    else if (!_envConfig.baseUrl.endsWith('/') && !path.startsWith('/')) {
      return '/$path';
    }
    // 其他情况直接返回
    return path;
  }

  /// 处理响应数据
  ///
  /// 简化的响应处理，业务状态码由ResponseInterceptor处理
  dynamic _handleResponse(Response response) {
    final data = response.data;
    Logger.debug('NetworkClient', '处理响应数据: $data, 状态码: ${response.statusCode}');

    // 直接返回响应数据，业务状态码已经在ResponseInterceptor中处理
    return data;
  }

  /// 从响应中提取数据
  ///
  /// 直接提取data字段，不再检查resultCode（因为_handleResponse已经检查过了）
  dynamic _extractData(dynamic response) {
    // 检查是否是API响应格式
    if (response is Map && response.containsKey('data')) {
      return response['data'];
    }

    // 如果不是标准API响应格式或没有data字段，直接返回数据
    return response;
  }



  /// GET请求
  ///
  /// [path] - 请求路径
  /// [queryParameters] - 查询参数
  /// [options] - 请求选项
  /// [cancelToken] - 取消令牌
  /// [onReceiveProgress] - 接收进度回调
  Future<dynamic> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final normalizedPath = _normalizePath(path);
      final response = await _dio.get(
        normalizedPath,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      final result = _handleResponse(response);
      return _extractData(result);
    } on DioException {
      // DioException直接重新抛出，让拦截器处理
      rethrow;
    } catch (e) {
      throw ServerException('$e');
    }
  }

  /// POST请求
  ///
  /// [path] - 请求路径
  /// [data] - 请求数据
  /// [queryParameters] - 查询参数
  /// [options] - 请求选项
  /// [cancelToken] - 取消令牌
  /// [onSendProgress] - 发送进度回调
  /// [onReceiveProgress] - 接收进度回调
  ///
  /// 如果响应包含标准API格式（resultCode/resultMsg/data），则返回整个响应
  /// 如果需要自动提取data字段，请设置 extractData=true
  Future<dynamic> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final normalizedPath = _normalizePath(path);
      final response = await _dio.post(
        normalizedPath,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      final result = _handleResponse(response);
      return _extractData(result);
    } on DioException {
      // DioException直接重新抛出，让拦截器处理
      rethrow;
    } catch (e) {
      throw ServerException('$e');
    }
  }

  /// PUT请求
  ///
  /// [path] - 请求路径
  /// [data] - 请求数据
  /// [queryParameters] - 查询参数
  /// [options] - 请求选项
  /// [cancelToken] - 取消令牌
  /// [onSendProgress] - 发送进度回调
  /// [onReceiveProgress] - 接收进度回调
  Future<dynamic> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final normalizedPath = _normalizePath(path);
      final response = await _dio.put(
        normalizedPath,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      final result = _handleResponse(response);
      return _extractData(result);
    } on DioException {
      // DioException直接重新抛出，让拦截器处理
      rethrow;
    } catch (e) {
      throw ServerException('$e');
    }
  }

  /// DELETE请求
  ///
  /// [path] - 请求路径
  /// [data] - 请求数据
  /// [queryParameters] - 查询参数
  /// [options] - 请求选项
  /// [cancelToken] - 取消令牌
  Future<dynamic> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final normalizedPath = _normalizePath(path);
      final response = await _dio.delete(
        normalizedPath,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      final result = _handleResponse(response);
      return _extractData(result);
    } on DioException {
      // DioException直接重新抛出，让拦截器处理
      rethrow;
    } catch (e) {
      throw ServerException('$e');
    }
  }




}
import 'package:flutter/material.dart';
import 'package:flutter_demo/core/router/app_navigator.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/constants.dart';
import '../widgets/safety_stat_card.dart';
import '../widgets/class_group_section.dart';

/// 教师端安全教育试题页面
///
/// 展示学生安全教育试题的完成情况，包括考试统计和学生列表
/// 支持按班级分组查看学生的安全教育试题分数
///
/// <AUTHOR>
/// @date 2025-04-28
/// @version 1.0
class TeacherSafetyEducationScreen extends StatefulWidget {
  const TeacherSafetyEducationScreen({Key? key}) : super(key: key);

  @override
  State<TeacherSafetyEducationScreen> createState() => _TeacherSafetyEducationScreenState();
}

class _TeacherSafetyEducationScreenState extends State<TeacherSafetyEducationScreen> {
  // 当前选中的课程名称
  String _courseName = '2021级市场销售2023-2024实习学年第二学期 岗位实习';

  // 可选课程列表
  final List<String> _availableCourses = [
    '2021级市场销售2023-2024实习学年第二学期 岗位实习',
    '2022级软件开发2023-2024实习学年第一学期 岗位实习',
    '2020级建筑工程2022-2023实习学年第二学期 岗位实习',
  ];

  @override
  Widget build(BuildContext context) {
    final List<Map<String, dynamic>> classGroups = [
      {
        'className': '建筑工程5班',
        'students': [
          {
            'name': '李成儒',
            'avatar': AppConstants.avatar1,
            'phone': '13569874562',
            'score': 80,
          },
          {
            'name': '陈诚',
            'avatar': AppConstants.avatar2,
            'phone': '13569874562',
            'score': 62,
          },
          {
            'name': '马婉亭',
            'avatar': AppConstants.avatar3,
            'phone': '13569874562',
            'score': 76,
          },
          {
            'name': '张洪涛',
            'avatar': AppConstants.avatar4,
            'phone': '13569874562',
            'score': 83,
          },
        ],
      },
    ];

    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: CustomAppBar(
        title: '实习生安全教育试题',
        onBackPressed: () {
          AppNavigator.goToHome(context);
        },
      ),
      body: Column(
        children: [
          // 实习/学期信息 - 固定在顶部
          CourseHeaderSection(
            courseName: _courseName,
            initialExpanded: false,
            availableCourses: _availableCourses,
            onCourseChanged: (newCourse) {
              setState(() {
                _courseName = newCourse;
              });
            },
          ),

          // 可滚动内容区域
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                const SizedBox(height: 16),

                // 统计卡片区域
                Container(
                  height: 138.h,
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: const Row(
                    children: [
                      Expanded(
                        child: SafetyStatCard(
                          icon: Icons.check_circle_outline,
                          label: '已考试',
                          numerator: 50,
                          denominator: 100,
                          color: Color(0xFF5DD1C3),
                          showRightBorder: false,
                        ),
                      ),
                      Expanded(
                        child: SafetyStatCard(
                          icon: Icons.chat_bubble_outline,
                          label: '已及格',
                          numerator: 26,
                          denominator: 50,
                          color: Color(0xFF00C853),
                          showRightBorder: false,
                        ),
                      ),
                      Expanded(
                        child: SafetyStatCard(
                          icon: Icons.insert_chart_outlined,
                          label: '80分以上',
                          numerator: 10,
                          denominator: 50,
                          color: Color(0xFF4B7CFF),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // 班级分组与学生列表
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    children: classGroups.map((group) => ClassGroupSection(
                      className: group['className'],
                      students: group['students'],
                    )).toList(),
                  ),
                ),
                // 底部留白，确保滚动到底部时有足够空间
                const SizedBox(height: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// -----
/// response_interceptor.dart
/// 
/// 响应拦截器，处理业务状态码和响应数据格式化
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dio/dio.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/core/error/exceptions/server_exception.dart';

/// 响应拦截器
///
/// 处理业务状态码，特别是认证相关的状态码
class ResponseInterceptor extends Interceptor {
  /// 日志标签
  static const String _tag = 'ResponseInterceptor';

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    Logger.info(_tag, '=== ResponseInterceptor.onResponse 开始 ===');
    Logger.info(_tag, '处理响应: ${response.requestOptions.path}');
    Logger.info(_tag, 'HTTP状态码: ${response.statusCode}');
    Logger.info(_tag, '响应数据: ${response.data}');

    try {
      // 首先检查HTTP状态码，如果是401直接处理
      if (response.statusCode == 401) {
        Logger.warning(_tag, '检测到HTTP 401状态码，直接转换为DioException');

        final dioException = DioException(
          requestOptions: response.requestOptions,
          response: response,
          type: DioExceptionType.badResponse,
          error: 'HTTP 401 Unauthorized',
        );

        Logger.warning(_tag, '抛出HTTP 401 DioException，将由AuthInterceptor处理');
        handler.reject(dioException);
        return;
      }

      // 检查响应数据格式中的业务状态码
      final data = response.data;

      if (data is Map && data.containsKey('resultCode')) {
        final resultCode = data['resultCode'];
        Logger.info(_tag, '检测到业务状态码: $resultCode');

        // 检查是否是认证失效的业务状态码
        if (_isAuthFailureCode(resultCode)) {
          Logger.warning(_tag, '检测到认证失效业务状态码: $resultCode，转换为401 HTTP状态码');

          // 创建401错误响应，这会触发AuthInterceptor的onError方法
          final errorResponse = Response(
            requestOptions: response.requestOptions,
            statusCode: 401,
            statusMessage: 'Unauthorized',
            data: data,
          );

          // 抛出DioException，这会被AuthInterceptor的onError方法捕获
          final dioException = DioException(
            requestOptions: response.requestOptions,
            response: errorResponse,
            type: DioExceptionType.badResponse,
            error: 'Authentication failed: $resultCode',
          );

          Logger.warning(_tag, '抛出业务状态码401 DioException，将由AuthInterceptor处理');
          handler.reject(dioException);
          return;
        }

        // 检查其他业务错误
        if (!_isSuccessCode(resultCode)) {
          Logger.warning(_tag, '业务错误: $resultCode, 消息: ${data['resultMsg']}');
          throw ServerException(data['resultMsg'] ?? '服务器错误');
        }
      }

      // 正常响应，继续处理
      Logger.info(_tag, '响应正常，继续处理');
      Logger.info(_tag, '=== ResponseInterceptor.onResponse 结束 ===');
      super.onResponse(response, handler);

    } catch (e) {
      Logger.error(_tag, 'ResponseInterceptor 捕获异常: $e');
      if (e is DioException) {
        // 如果是DioException，传递给错误处理器（包括AuthInterceptor）
        Logger.warning(_tag, '传递DioException给错误处理器: ${e.message}');
        handler.reject(e);
      } else if (e is ServerException) {
        // 如果是ServerException，转换为DioException
        Logger.warning(_tag, '转换ServerException为DioException: ${e.message}');
        final dioException = DioException(
          requestOptions: response.requestOptions,
          error: e.message,
          type: DioExceptionType.unknown,
        );
        handler.reject(dioException);
      } else {
        // 其他异常
        Logger.error(_tag, '未知异常: $e');
        final dioException = DioException(
          requestOptions: response.requestOptions,
          error: e.toString(),
          type: DioExceptionType.unknown,
        );
        handler.reject(dioException);
      }
    }
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    Logger.info(_tag, '=== ResponseInterceptor.onError 开始 ===');
    Logger.info(_tag, '错误类型: ${err.type}');
    Logger.info(_tag, 'HTTP状态码: ${err.response?.statusCode}');
    Logger.info(_tag, '错误消息: ${err.message}');
    Logger.info(_tag, '响应数据: ${err.response?.data}');

    // 如果是HTTP 401错误，直接传递给下一个拦截器（AuthInterceptor）
    if (err.response?.statusCode == 401) {
      Logger.warning(_tag, '检测到HTTP 401错误，传递给AuthInterceptor处理');
      Logger.info(_tag, '=== ResponseInterceptor.onError 结束（传递401错误）===');
      super.onError(err, handler);
      return;
    }

    // 其他错误也传递给下一个拦截器
    Logger.info(_tag, '传递其他错误给下一个拦截器');
    Logger.info(_tag, '=== ResponseInterceptor.onError 结束 ===');
    super.onError(err, handler);
  }
  
  /// 检查是否是认证失效的状态码
  bool _isAuthFailureCode(dynamic resultCode) {
    // 转换为字符串进行比较，确保兼容不同的数据类型
    final codeStr = resultCode.toString();
    Logger.debug(_tag, '检查认证失效状态码: $resultCode (类型: ${resultCode.runtimeType}, 字符串: $codeStr)');

    final isAuthFailure = codeStr == '10401' ||
                         codeStr == '401' ||
                         resultCode == 10401 ||
                         resultCode == 401;

    Logger.debug(_tag, '是否为认证失效状态码: $isAuthFailure');
    return isAuthFailure;
  }
  
  /// 检查是否是成功状态码
  bool _isSuccessCode(dynamic resultCode) {
    return resultCode == 200 || 
           resultCode == '200' || 
           resultCode == '0' || 
           resultCode == 0;
  }
}

/// -----
/// file_upload_widget.dart
///
/// 文件上传组件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

/// 文件上传组件
class FileUploadWidget extends StatefulWidget {
  /// 文件选择回调
  final ValueChanged<List<File>>? onFilesSelected;

  const FileUploadWidget({
    Key? key,
    this.onFilesSelected,
  }) : super(key: key);

  @override
  State<FileUploadWidget> createState() => _FileUploadWidgetState();
}

class _FileUploadWidgetState extends State<FileUploadWidget> {
  final List<File> _selectedFiles = [];
  final int _maxFiles = 20;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 文件上传标题
          Text(
            '文件上传',
            style: TextStyle(
              fontSize: 32.sp,
              color: AppTheme.black333,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),

          // 图片网格
          _buildImageGrid(),




        ],
      ),
    );
  }

  /// 构建图片网格
  Widget _buildImageGrid() {
    return Container(
      padding: EdgeInsets.all(30.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 3,
            mainAxisSpacing: 12.w,
            crossAxisSpacing: 12.w,
            childAspectRatio: 1,
            children: [
              ..._selectedFiles.map((file) => _buildImageItem(file)),
              if (_selectedFiles.length < _maxFiles)
                _buildAddImageButton(),
            ],
          ),
          SizedBox(height: 30.h),
          // 上传提示
          Text(
            '目前最多支持上传20张图片，每张图片大小不超过5M',
            style: TextStyle(
              fontSize: 24.sp,
              color: AppTheme.black999,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建图片项
  Widget _buildImageItem(File file) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: const Color(0xFFE5E5E5)),
            image: DecorationImage(
              image: FileImage(file),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: -6.w,
          right: -6.w,
          child: GestureDetector(
            onTap: () {
              setState(() {
                _selectedFiles.remove(file);
              });
              widget.onFilesSelected?.call(_selectedFiles);
            },
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                size: 20,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建添加图片按钮
  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: _pickImage,
      child: Container(
        width: 100,  // 内部灰色Container也横向铺满（减去padding）
        height: 100,  // 设置一个固定高度（根据需求调整）
        decoration: BoxDecoration(
          color: const Color(0xFFF5F5F5),
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Center(
          child: Icon(
            Icons.add,
            size: 40,
            color: const Color(0xFFCCCCCC),
          ),
        ),
      ),
    );
  }

  /// 选择图片
  Future<void> _pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        final file = File(pickedFile.path);

        // 检查文件大小（5MB限制）
        final fileSize = await file.length();
        if (fileSize > 5 * 1024 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('图片大小不能超过5MB')),
            );
          }
          return;
        }

        setState(() {
          _selectedFiles.add(file);
        });

        widget.onFilesSelected?.call(_selectedFiles);
      }
    } catch (e) {
      debugPrint('Error picking image: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('选择图片失败，请重试')),
        );
      }
    }
  }
}

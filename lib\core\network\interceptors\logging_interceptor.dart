import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_demo/core/utils/logger.dart';

/// 日志拦截器
///
/// 记录请求和响应的详细信息，便于调试
/// 在开发环境中启用，生产环境可以禁用
class LoggingInterceptor extends Interceptor {
  static const String _tag = 'NetworkClient';
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final requestPath = _buildFullUrl(options.baseUrl, options.path);


    // 控制台输出格式，方便调试
    if (kDebugMode) {
      debugPrint('┌─────────────────────────────────────────────────────');
      debugPrint('│ 请求: ${options.method} $requestPath');
      debugPrint('│ 请求头: ${_prettyJson(options.headers)}');

      if (options.queryParameters.isNotEmpty) {
        debugPrint('│ 查询参数: ${_prettyJson(options.queryParameters)}');
      }

      if (options.data != null) {
        debugPrint('│ 请求体: ${_prettyJson(options.data)}');
      }

      debugPrint('└─────────────────────────────────────────────────────');
    }

    super.onRequest(options, handler);
  }

  /// 脱敏敏感数据
  dynamic _maskSensitiveData(dynamic data) {
    if (data is! Map) {
      return data;
    }

    final Map<String, dynamic> maskedData = Map.from(data);

    // 脱敏密码字段
    final sensitiveFields = ['password', 'pwd', 'secret', 'token'];
    for (final field in sensitiveFields) {
      if (maskedData.containsKey(field) && maskedData[field] is String) {
        maskedData[field] = Logger.maskSensitiveInfo(maskedData[field]);
      }
    }

    return maskedData;
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    final requestPath = _buildFullUrl(response.requestOptions.baseUrl, response.requestOptions.path);

    // 控制台输出格式，方便调试
    if (kDebugMode) {
      debugPrint('┌─────────────────────────────────────────────────────');
      debugPrint('│ 响应: ${response.statusCode} ${response.statusMessage} $requestPath');
      debugPrint('│ 响应头: ${_prettyJson(response.headers.map)}');

      if (response.data != null) {
        debugPrint('│ 响应体: ${_prettyJson(response.data)}');
      }

      debugPrint('└─────────────────────────────────────────────────────');
    }

    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final requestPath = _buildFullUrl(err.requestOptions.baseUrl, err.requestOptions.path);

    // 使用新的Logger记录错误信息
    Logger.error(
      _tag,
      '请求失败: ${err.type} $requestPath - ${err.message}',
      exception: err,
      stackTrace: err.stackTrace,
    );

    // 记录响应信息
    if (err.response != null) {
      Logger.error(
        _tag,
        '错误响应: ${err.response?.statusCode} - ${Logger.prettyJson(err.response?.data)}',
      );
    }

    if (kDebugMode) {
      debugPrint('┌─────────────────────────────────────────────────────');
      debugPrint('│ 错误: ${err.type} $requestPath');
      debugPrint('│ 错误信息: ${err.message}');

      if (err.response != null) {
        debugPrint('│ 响应状态码: ${err.response?.statusCode}');
        debugPrint('│ 响应体: ${_prettyJson(err.response?.data)}');
      }

      debugPrint('└─────────────────────────────────────────────────────');
    }

    super.onError(err, handler);
  }

  /// 构建完整URL，避免重复斜杠
  String _buildFullUrl(String baseUrl, String path) {
    if (baseUrl.endsWith('/') && path.startsWith('/')) {
      // 如果baseUrl以/结尾，且path以/开头，则移除path中的/
      return baseUrl + path.substring(1);
    } else if (!baseUrl.endsWith('/') && !path.startsWith('/')) {
      // 如果baseUrl不以/结尾，且path不以/开头，则添加/
      return '$baseUrl/$path';
    } else {
      // 其他情况直接拼接
      return baseUrl + path;
    }
  }

  /// 格式化JSON数据
  ///
  /// 将JSON数据格式化为易读的字符串
  String _prettyJson(dynamic json) {
    try {
      if (json is Map || json is List) {
        const encoder = JsonEncoder.withIndent('  ');
        return encoder.convert(json);
      } else if (json is String) {
        // 尝试将字符串解析为JSON
        try {
          const encoder = JsonEncoder.withIndent('  ');
          return encoder.convert(jsonDecode(json));
        } on FormatException {
          // 如果不是有效的JSON字符串，则直接返回
          return json;
        }
      } else {
        return json.toString();
      }
    } on Exception {
      return json.toString();
    }
  }
}
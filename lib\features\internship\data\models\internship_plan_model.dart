/// -----
/// internship_plan_model.dart
///
/// 实习计划数据模型，用于存储实习计划的数据
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import '../../domain/entities/internship_plan.dart';

class InternshipPlanModel extends Equatable {
  final String id;
  final String academicYear;
  final String grade;
  final int planType;
  final int practiceMode;
  final String planName;
  final String planCode;
  final String facultyId;
  final String? facultyName;
  final String majorId;
  final String? majorName;
  final String? majorDirection;
  final String semester;
  final String? schoolId;
  final int status;
  final int dr;
  final String createPerson;
  final int createTime;
  final int startTime;
  final int endTime;
  final String level;
  final String subsidy;
  final String majorAdmin;
  final String? majorAdminName;
  final String? file;
  final String? aiSummary;
  final int planStatus;
  final String? requiredFiles;
  final List<String>? requiredFileList;
  final int signNum;
  final int dailyReportNum;
  final int weeklyReportNum;
  final int monthlyReportNum;
  final int summaryNum;
  final double scoreEnterpriseTeacher;
  final double scoreSchoolTeacher;
  final double scoreProcess;
  final int? safeExamQuestionCount;
  final int? checkNum;
  final int? mulCheckNum;
  final int? safeExamEachTimeCount;

  const InternshipPlanModel({
    required this.id,
    required this.academicYear,
    required this.grade,
    required this.planType,
    required this.practiceMode,
    required this.planName,
    required this.planCode,
    required this.facultyId,
    this.facultyName,
    required this.majorId,
    this.majorName,
    this.majorDirection,
    required this.semester,
    this.schoolId,
    required this.status,
    required this.dr,
    required this.createPerson,
    required this.createTime,
    required this.startTime,
    required this.endTime,
    required this.level,
    required this.subsidy,
    required this.majorAdmin,
    this.majorAdminName,
    this.file,
    this.aiSummary,
    required this.planStatus,
    this.requiredFiles,
    this.requiredFileList,
    required this.signNum,
    required this.dailyReportNum,
    required this.weeklyReportNum,
    required this.monthlyReportNum,
    required this.summaryNum,
    required this.scoreEnterpriseTeacher,
    required this.scoreSchoolTeacher,
    required this.scoreProcess,
    this.safeExamQuestionCount,
    this.checkNum,
    this.mulCheckNum,
    this.safeExamEachTimeCount,
  });

  // 从JSON创建模型
  factory InternshipPlanModel.fromJson(Map<String, dynamic> json) {
    return InternshipPlanModel(
      id: json['id']?.toString() ?? '',
      academicYear: json['academicYear']?.toString() ?? '',
      grade: json['grade']?.toString() ?? '',
      planType: json['planType']?.toInt() ?? 0,
      practiceMode: json['practiceMode']?.toInt() ?? 0,
      planName: json['planName']?.toString() ?? '',
      planCode: json['planCode']?.toString() ?? '',
      facultyId: json['facultyId']?.toString() ?? '',
      facultyName: json['facultyName']?.toString(),
      majorId: json['majorId']?.toString() ?? '',
      majorName: json['majorName']?.toString(),
      majorDirection: json['majorDirection']?.toString(),
      semester: json['semester']?.toString() ?? '',
      schoolId: json['schoolId']?.toString(),
      status: json['status']?.toInt() ?? 0,
      dr: json['dr']?.toInt() ?? 0,
      createPerson: json['createPerson']?.toString() ?? '',
      createTime: json['createTime']?.toInt() ?? 0,
      startTime: json['startTime']?.toInt() ?? 0,
      endTime: json['endTime']?.toInt() ?? 0,
      level: json['level']?.toString() ?? '',
      subsidy: json['subsidy']?.toString() ?? '',
      majorAdmin: json['majorAdmin']?.toString() ?? '',
      majorAdminName: json['majorAdminName']?.toString(),
      file: json['file']?.toString(),
      aiSummary: json['aiSummary']?.toString(),
      planStatus: json['planStatus']?.toInt() ?? 0,
      requiredFiles: json['requiredFiles']?.toString(),
      requiredFileList: json['requiredFileList'] != null
          ? List<String>.from(json['requiredFileList'])
          : null,
      signNum: json['signNum']?.toInt() ?? 0,
      dailyReportNum: json['dailyReportNum']?.toInt() ?? 0,
      weeklyReportNum: json['weeklyReportNum']?.toInt() ?? 0,
      monthlyReportNum: json['monthlyReportNum']?.toInt() ?? 0,
      summaryNum: json['summaryNum']?.toInt() ?? 0,
      scoreEnterpriseTeacher: json['scoreEnterpriseTeacher']?.toDouble() ?? 0.0,
      scoreSchoolTeacher: json['scoreSchoolTeacher']?.toDouble() ?? 0.0,
      scoreProcess: json['scoreProcess']?.toDouble() ?? 0.0,
      safeExamQuestionCount: json['safeExamQuestionCount']?.toInt(),
      checkNum: json['checkNum']?.toInt(),
      mulCheckNum: json['mulCheckNum']?.toInt(),
      safeExamEachTimeCount: json['safeExamEachTimeCount']?.toInt(),
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'academicYear': academicYear,
      'grade': grade,
      'planType': planType,
      'practiceMode': practiceMode,
      'planName': planName,
      'planCode': planCode,
      'facultyId': facultyId,
      'facultyName': facultyName,
      'majorId': majorId,
      'majorName': majorName,
      'majorDirection': majorDirection,
      'semester': semester,
      'schoolId': schoolId,
      'status': status,
      'dr': dr,
      'createPerson': createPerson,
      'createTime': createTime,
      'startTime': startTime,
      'endTime': endTime,
      'level': level,
      'subsidy': subsidy,
      'majorAdmin': majorAdmin,
      'majorAdminName': majorAdminName,
      'file': file,
      'aiSummary': aiSummary,
      'planStatus': planStatus,
      'requiredFiles': requiredFiles,
      'requiredFileList': requiredFileList,
      'signNum': signNum,
      'dailyReportNum': dailyReportNum,
      'weeklyReportNum': weeklyReportNum,
      'monthlyReportNum': monthlyReportNum,
      'summaryNum': summaryNum,
      'scoreEnterpriseTeacher': scoreEnterpriseTeacher,
      'scoreSchoolTeacher': scoreSchoolTeacher,
      'scoreProcess': scoreProcess,
      'safeExamQuestionCount': safeExamQuestionCount,
      'checkNum': checkNum,
      'mulCheckNum': mulCheckNum,
      'safeExamEachTimeCount': safeExamEachTimeCount,
    };
  }

  // 转换为领域实体
  InternshipPlan toEntity() {
    return InternshipPlan(
      id: id,
      planName: planName,
      planCode: planCode,
      academicYear: academicYear,
      semester: semester,
      grade: grade,
      level: level,
      majorName: majorName ?? '',
      facultyName: facultyName ?? '',
      planStatus: planStatus,
      planType: planType,
      practiceMode: practiceMode,
      startTime: startTime,
      endTime: endTime,
      createPerson: createPerson,
      createTime: createTime,
      majorAdminName: majorAdminName ?? '',
      subsidy: subsidy,
      dailyReportNum: dailyReportNum,
      weeklyReportNum: weeklyReportNum,
      monthlyReportNum: monthlyReportNum,
      summaryNum: summaryNum,
      signNum: signNum,
      checkNum: checkNum ?? 0,
      mulCheckNum: mulCheckNum ?? 0,
    );
  }

  @override
  List<Object?> get props => [
    id,
    academicYear,
    grade,
    planType,
    practiceMode,
    planName,
    planCode,
    facultyId,
    facultyName,
    majorId,
    majorName,
    majorDirection,
    semester,
    schoolId,
    status,
    dr,
    createPerson,
    createTime,
    startTime,
    endTime,
    level,
    subsidy,
    majorAdmin,
    majorAdminName,
    file,
    aiSummary,
    planStatus,
    requiredFiles,
    requiredFileList,
    signNum,
    dailyReportNum,
    weeklyReportNum,
    monthlyReportNum,
    summaryNum,
    scoreEnterpriseTeacher,
    scoreSchoolTeacher,
    scoreProcess,
    safeExamQuestionCount,
    checkNum,
    mulCheckNum,
    safeExamEachTimeCount,
  ];

  // 获取示例数据
  static List<InternshipPlanModel> getSampleData() {
    return [
      InternshipPlanModel(
        id: '1',
        planName: '2021级市场销售2023-2024实习学年第二学期岗位实习',
        planCode: 'PLAN001',
        academicYear: '2024-2025',
        semester: '2024-2025实习学年第一学期',
        grade: '2021',
        level: '本科',
        majorName: '市场销售',
        facultyName: '商学院',
        planStatus: 1,
        planType: 1,
        practiceMode: 1,
        startTime: 1714060800000, // 2024-04-25
        endTime: 1724515200000, // 2024-08-25
        createPerson: '张三',
        createTime: 1713888720000, // 2024-04-23 22:12
        majorAdminName: '李教授',
        subsidy: '500元/月',
        dailyReportNum: 30,
        weeklyReportNum: 4,
        monthlyReportNum: 1,
        summaryNum: 1,
        signNum: 60,
        checkNum: 10,
        mulCheckNum: 5,
        aiSummary: '',
        facultyId: '1',
        majorAdmin: '1001',
        majorDirection: '销售管理',
        majorId: '10',
        status: 1,
        dr: 0,
        scoreEnterpriseTeacher: 0.0,
        scoreSchoolTeacher: 0.0,
        scoreProcess: 0.0,
      ),
    ];
  }
}

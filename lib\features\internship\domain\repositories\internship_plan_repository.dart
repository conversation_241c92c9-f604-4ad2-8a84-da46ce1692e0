/// -----
/// internship_plan_repository.dart
/// 
/// 实习计划仓库接口定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../entities/internship_plan.dart';

/// 实习计划仓库接口
///
/// 定义实习计划数据操作的抽象契约
abstract class InternshipPlanRepository {
  /// 获取教师的实习计划列表
  ///
  /// 返回 [Either] 类型，左侧为失败信息，右侧为实习计划列表
  Future<Either<Failure, List<InternshipPlan>>> getTeacherInternshipPlans();

  /// 获取实习计划详情
  ///
  /// [id] 实习计划ID
  /// 返回 [Either] 类型，左侧为失败信息，右侧为实习计划详情
  Future<Either<Failure, InternshipPlan>> getInternshipPlanDetail(String id);
}

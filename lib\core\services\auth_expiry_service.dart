/// -----
/// auth_expiry_service.dart
/// 
/// 认证失效处理服务，负责处理token过期和用户认证失效的情况
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../constants/constants.dart';
import '../router/app_router.dart';
import '../router/route_constants.dart';
import '../storage/local_storage.dart';
import '../utils/logger.dart';
import '../widgets/app_snack_bar.dart';

/// 认证失效处理服务
/// 
/// 提供统一的认证失效处理逻辑，包括清除认证数据和跳转登录页面
class AuthExpiryService {
  static const String _tag = 'AuthExpiryService';
  
  final LocalStorage _localStorage;
  
  AuthExpiryService(this._localStorage);

  /// 处理认证失效
  /// 
  /// 当检测到401错误或token失效时调用此方法
  /// [context] - 当前的BuildContext，用于显示提示和导航
  /// [message] - 可选的自定义提示消息
  Future<void> handleAuthExpiry({
    BuildContext? context,
    String? message,
  }) async {
    Logger.warning(_tag, '处理认证失效');

    try {
      // 1. 清除本地认证数据
      await _clearAuthData();

      // 2. 显示用户提示
      if (context != null && context.mounted) {
        final displayMessage = message ?? '登录已过期，请重新登录';
        AppSnackBar.showWarning(context, displayMessage);
        
        // 3. 延迟一下再跳转，让用户看到提示
        await Future.delayed(const Duration(milliseconds: 1500));
        
        // 4. 跳转到登录页面
        if (context.mounted) {
          await _navigateToLogin(context);
        }
      }

      Logger.info(_tag, '认证失效处理完成');
    } on Exception catch (e, s) {
      Logger.error(
        _tag,
        '处理认证失效时发生错误',
        exception: e,
        stackTrace: s,
      );
    }
  }

  /// 处理认证失效（无Context版本）
  ///
  /// 当在拦截器等无法获取BuildContext的地方检测到401错误时调用此方法
  /// [message] - 可选的自定义提示消息
  Future<void> handleAuthExpiryWithoutContext({
    String? message,
  }) async {
    Logger.warning(_tag, '=== AuthExpiryService.handleAuthExpiryWithoutContext 开始 ===');
    Logger.warning(_tag, '处理认证失效（无Context版本）');

    try {
      // 1. 清除本地认证数据
      Logger.info(_tag, '步骤1: 清除本地认证数据');
      await _clearAuthData();

      // 2. 尝试获取全局导航上下文
      Logger.info(_tag, '步骤2: 获取全局导航上下文');
      final context = AppRouter.rootNavigatorKey.currentContext;
      Logger.info(_tag, '导航上下文: ${context != null ? "已获取" : "未获取"}');

      if (context != null && context.mounted) {
        Logger.info(_tag, '步骤3: 显示用户提示');
        // 3. 显示用户提示
        final displayMessage = message ?? '登录已过期，请重新登录';
        AppSnackBar.showWarning(context, displayMessage);
        Logger.info(_tag, '提示消息已显示: $displayMessage');

        // 4. 延迟一下再跳转，让用户看到提示
        Logger.info(_tag, '步骤4: 等待1.5秒');
        await Future.delayed(const Duration(milliseconds: 1500));

        // 5. 跳转到登录页面
        Logger.info(_tag, '步骤5: 跳转到登录页面');
        if (context.mounted) {
          await _navigateToLogin(context);
        } else {
          Logger.warning(_tag, 'Context已不可用，无法跳转');
        }
      } else {
        // 如果无法获取Context，直接跳转
        Logger.warning(_tag, '无法获取Context，直接跳转');
        await _navigateToLogin(null);
      }

      Logger.info(_tag, '✅ 认证失效处理完成（无Context版本）');
      Logger.info(_tag, '=== AuthExpiryService.handleAuthExpiryWithoutContext 结束 ===');
    } on Exception catch (e, s) {
      Logger.error(
        _tag,
        '❌ 处理认证失效时发生错误（无Context版本）',
        exception: e,
        stackTrace: s,
      );
      Logger.error(_tag, '=== AuthExpiryService.handleAuthExpiryWithoutContext 异常结束 ===');
    }
  }

  /// 清除认证数据
  /// 
  /// 清除本地存储的所有认证相关信息
  Future<void> _clearAuthData() async {
    Logger.info(_tag, '开始清除本地认证数据');

    try {
      // 清除token
      await _localStorage.remove(AppConstants.tokenKey);
      
      // 清除用户信息
      await _localStorage.remove(AppConstants.userKey);
      await _localStorage.remove(AppConstants.userNameKey);
      await _localStorage.remove(AppConstants.userPhoneKey);
      await _localStorage.remove(AppConstants.userTypeKey);
      
      // 清除登录状态
      await _localStorage.remove(AppConstants.isLoggedInKey);
      
      // 清除记住的登录信息（可选）
      await _localStorage.remove(AppConstants.phoneKey);
      await _localStorage.remove(AppConstants.passwordKey);

      Logger.info(_tag, '本地认证数据清除完成');
    } catch (e, s) {
      Logger.error(
        _tag,
        '清除认证数据时发生错误',
        exception: e,
        stackTrace: s,
      );
      rethrow;
    }
  }

  /// 导航到登录页面
  ///
  /// 使用go_router跳转到登录页面，并清除导航栈
  Future<void> _navigateToLogin(BuildContext? context) async {
    Logger.info(_tag, '导航到登录页面');

    try {
      // 优先使用传入的context，如果没有则使用全局导航器
      BuildContext? targetContext = context;
      if (targetContext == null || !targetContext.mounted) {
        targetContext = AppRouter.rootNavigatorKey.currentContext;
      }

      if (targetContext != null && targetContext.mounted) {
        // 使用go方法跳转到登录页面，这会清除当前的导航栈
        targetContext.go(AppRoutes.login);
        Logger.info(_tag, '已跳转到登录页面');
      } else {
        Logger.warning(_tag, '无法获取有效的导航上下文');
      }
    } on Exception catch (e, s) {
      Logger.error(
        _tag,
        '导航到登录页面时发生错误',
        exception: e,
        stackTrace: s,
      );
    }
  }

  /// 检查是否已登录
  /// 
  /// 检查本地存储中是否有有效的认证信息
  Future<bool> isLoggedIn() async {
    try {
      final token = _localStorage.getString(AppConstants.tokenKey);
      final isLoggedIn = _localStorage.getBool(AppConstants.isLoggedInKey) ?? false;
      
      return token != null && token.isNotEmpty && isLoggedIn;
    } catch (e, s) {
      Logger.error(
        _tag,
        '检查登录状态时发生错误',
        exception: e,
        stackTrace: s,
      );
      return false;
    }
  }

  /// 获取当前用户token
  /// 
  /// 返回当前存储的用户token，如果不存在则返回null
  String? getCurrentToken() {
    try {
      return _localStorage.getString(AppConstants.tokenKey);
    } catch (e, s) {
      Logger.error(
        _tag,
        '获取当前token时发生错误',
        exception: e,
        stackTrace: s,
      );
      return null;
    }
  }
}

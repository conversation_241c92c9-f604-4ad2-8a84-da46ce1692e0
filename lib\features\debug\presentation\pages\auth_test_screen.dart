/// -----
/// auth_test_screen.dart
/// 
/// 认证测试页面，用于测试401错误处理逻辑
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/app_snack_bar.dart';

/// 认证测试页面
class AuthTestScreen extends StatefulWidget {
  const AuthTestScreen({Key? key}) : super(key: key);

  @override
  State<AuthTestScreen> createState() => _AuthTestScreenState();
}

class _AuthTestScreenState extends State<AuthTestScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: '401错误测试'),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              '测试401错误处理逻辑',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              '点击下面的按钮来触发一个会返回401错误的API请求，测试应用是否会自动跳转到登录页面。',
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _isLoading ? null : _testFileUploadApi,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('测试文件上传API (会返回401)'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _testGeneric401,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('测试通用401错误'),
            ),
            const SizedBox(height: 32),
            const Text(
              '预期行为：',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('1. 显示警告提示："登录已过期，请重新登录"'),
            const Text('2. 1.5秒后自动跳转到登录页面'),
            const Text('3. 清除本地认证数据'),
          ],
        ),
      ),
    );
  }

  /// 测试文件上传API
  Future<void> _testFileUploadApi() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final dioClient = GetIt.instance<DioClient>();
      
      // 这个API会返回401错误
      await dioClient.get(
        'internshipservice/v1/internship/student/file/require/list',
        queryParameters: {'planId': '8'},
      );
      
      // 如果到达这里，说明没有抛出异常
      if (mounted) {
        AppSnackBar.showError(context, '测试失败：API没有返回401错误');
      }
    } catch (e) {
      // 这里应该会捕获到异常，但AuthInterceptor应该已经处理了401错误
      print('捕获到异常: $e');
      if (mounted) {
        AppSnackBar.show(context, '捕获到异常: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 测试通用401错误
  Future<void> _testGeneric401() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final dioClient = GetIt.instance<DioClient>();
      
      // 尝试访问一个需要认证的API
      await dioClient.get('userservice/v1/user/profile');
      
      // 如果到达这里，说明没有抛出异常
      if (mounted) {
        AppSnackBar.showError(context, '测试失败：API没有返回401错误');
      }
    } catch (e) {
      // 这里应该会捕获到异常，但AuthInterceptor应该已经处理了401错误
      print('捕获到异常: $e');
      if (mounted) {
        AppSnackBar.show(context, '捕获到异常: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

import 'package:dio/dio.dart';
import 'package:flutter_demo/core/utils/logger.dart';

import '../../../../../core/error/exceptions/server_exception.dart';
import '../../../../../core/network/dio_client.dart';
import '../../models/user_model.dart';

/// 认证远程数据源接口
///
/// 定义与远程 API 交互的方法
abstract class AuthRemoteDataSource {
  /// 登录
  ///
  /// 参数：[phone] 用户名，[password] 密码
  /// 返回：UserModel 用户模型
  /// 抛出：ServerException 服务器异常
  Future<UserModel> login({
    required String phone,
    required String password,
  });

  /// 注册
  ///
  /// 参数：[phone] 手机号，[code] 验证码，[password] 密码
  /// 返回：bool 是否成功
  /// 抛出：ServerException 服务器异常
  Future<bool> register({
    required String phone,
    required String code,
    required String password,
  });

  /// 重置密码
  ///
  /// 参数：[phone] 手机号，[code] 验证码，[newPassword] 新密码
  /// 返回：ApiResponse 响应结果
  /// 抛出：ServerException 服务器异常
  Future<String> resetPassword({
    required String phone,
    required String code,
    required String newPassword,
  });

  /// 退出登录
  ///
  /// 返回：bool 是否成功
  /// 抛出：ServerException 服务器异常
  Future<bool> logout();

  /// 发送验证码
  ///
  /// 参数：[phone] 手机号，[deviceId] 设备ID
  /// 返回：ApiResponse 响应结果
  /// 抛出：ServerException 服务器异常
  Future<String> sendVerificationCode({
    required String phone,
    String? deviceId,
  });

  /// 认证用户
  Future<int> authenticate({
    required String deptName,
    required String userCode,
    required String userName,
    required String? userType,
  });
}

/// 认证远程数据源实现
///
/// 实现与远程 API 交互的方法
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final DioClient _dioClient;

  AuthRemoteDataSourceImpl(this._dioClient);

  @override
  Future<UserModel> login({
    required String phone,
    required String password,
  }) async {
    try {
      final userData = await _dioClient.post(
        'userservice/v1/user/login',
        data: {
          'phone': phone,
          'password': password,
        },
      );

      return UserModel.fromLoginResponse(userData);
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('登录失败: $e');
    }
  }

  @override
  Future<bool> register({
    required String phone,
    required String code,
    required String password,
  }) async {
    try {
      await _dioClient.post(
        'userservice/v1/user/register',
        data: {
          'phone': phone,
          'code': code,
          'password': password,
        },
      );

      return true;
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('注册失败: $e');
    }
  }

  @override
  Future<String> resetPassword({
    required String phone,
    required String code,
    required String newPassword,
  }) async {
    try {
      final response = await _dioClient.post(
        'userservice/v1/user/resetPassword',
        data: {
          'phone': phone,
          'code': code,
          'newPassword': newPassword,
        },
      );

      return response;
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('重置密码失败: $e');
    }
  }

  @override
  Future<bool> logout() async {
    try {
      await _dioClient.post('/user/logout');
      return true;
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('退出登录失败: $e');
    }
  }

  @override
  Future<String> sendVerificationCode({
    required String phone,
    String? deviceId,
  }) async {
    try {
      final response = await _dioClient.post(
        'userservice/v1/user/sendSmsCode',
        data: {
          'd': deviceId, // 设备ID，如果未提供则使用默认值
          'phone': phone,
        },
      );

      return response;
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('发送验证码失败: $e');
    }
  }

  @override
  Future<int> authenticate({required String deptName, required String userCode,
    required String userName, required String? userType}) async {
    try {
      final response = await _dioClient.post(
        'userservice/v1/user/authenticate',
        data: {
          'deptName': deptName,
          'userCode': userCode,
          'userName': userName,
          'userType': userType,
        },
      );
      return 0;
    } catch (e) {
      throw ServerException('$e');
    }
  }
}

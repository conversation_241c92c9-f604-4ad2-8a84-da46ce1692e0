/// -----
/// file_upload_state.dart
/// 
/// 文件上传BLoC状态定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import '../../domain/entities/file_requirement.dart';

/// 文件上传状态基类
abstract class FileUploadState extends Equatable {
  const FileUploadState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class FileUploadInitial extends FileUploadState {}

/// 加载中状态
class FileUploadLoading extends FileUploadState {
  /// 是否是首次加载
  final bool isFirstFetch;
  /// 旧的文件要求列表（用于刷新时保持UI）
  final List<FileRequirement> oldFileRequirements;

  const FileUploadLoading({
    this.isFirstFetch = false,
    this.oldFileRequirements = const [],
  });

  @override
  List<Object?> get props => [isFirstFetch, oldFileRequirements];
}

/// 加载成功状态
class FileUploadSuccess extends FileUploadState {
  /// 文件要求列表
  final List<FileRequirement> fileRequirements;

  const FileUploadSuccess({
    required this.fileRequirements,
  });

  @override
  List<Object?> get props => [fileRequirements];
}

/// 加载失败状态
class FileUploadFailure extends FileUploadState {
  /// 错误消息
  final String message;
  /// 旧的文件要求列表（用于错误时保持UI）
  final List<FileRequirement> fileRequirements;

  const FileUploadFailure({
    required this.message,
    this.fileRequirements = const [],
  });

  @override
  List<Object?> get props => [message, fileRequirements];
}

/// 刷新成功状态
class FileUploadRefreshSuccess extends FileUploadSuccess {
  const FileUploadRefreshSuccess({
    required List<FileRequirement> fileRequirements,
  }) : super(fileRequirements: fileRequirements);
}

/// 刷新失败状态
class FileUploadRefreshFailure extends FileUploadFailure {
  const FileUploadRefreshFailure({
    required String message,
    List<FileRequirement> fileRequirements = const [],
  }) : super(
          message: message,
          fileRequirements: fileRequirements,
        );
}

import 'package:equatable/equatable.dart';

/// -----
/// user.dart
/// 
/// 用户实体类，表示系统中的用户
///
/// <AUTHOR>
/// @date 2025-05-28
/// @copyright Copyright © 2025 亿硕教育
/// -----

/// 用户实体
///
/// 表示系统中的用户，包含用户的基本信息
class User extends Equatable {
  /// 用户唯一标识
  final String userKey;
  
  /// 部门/学校名称
  final String? deptName;
  
  /// 部门信息
  final String? dept;
  
  /// 用户类型（1=学生，2=教师，3=企业HR）
  final String? userType;
  
  /// 用户名称
  final String? userName;
  
  /// 用户编号/学号/工号
  final String? userCode;
  
  /// 访问令牌
  final String? token;

  const User({
    required this.userKey,
    this.deptName,
    this.dept,
    this.userType,
    this.userName,
    this.userCode,
    this.token,
  });

  @override
  List<Object?> get props => [
        userKey,
        deptName,
        dept,
        userType,
        userName,
        userCode,
        token,
      ];
}

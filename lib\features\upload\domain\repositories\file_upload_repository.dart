/// -----
/// file_upload_repository.dart
/// 
/// 文件上传仓库接口，定义文件上传相关的数据操作
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../entities/file_requirement.dart';

/// 文件上传仓库接口
/// 
/// 定义文件上传相关的操作
abstract class FileUploadRepository {
  /// 获取文件要求列表
  /// 
  /// [planId] 计划ID
  /// 返回 Either<Failure, List<FileRequirement>>，表示获取文件要求列表成功或失败
  Future<Either<Failure, List<FileRequirement>>> getFileRequirements({
    required String planId,
  });
}

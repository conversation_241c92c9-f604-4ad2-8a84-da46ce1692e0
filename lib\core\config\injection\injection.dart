import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../network/dio_client.dart';
import '../../network/network_info.dart';
import '../../services/auth_expiry_service.dart';
import '../../storage/local_storage.dart';
import '../../storage/local_storage_impl.dart';
import '../../utils/logger.dart';
import '../../../features/auth/di/auth_injection.dart';
import '../../../features/splash/di/splash_injection.dart';
import '../../../features/safety/di/safety_injection.dart';
import '../../../features/internship/di/internship_injection.dart';
import '../../../features/upload/di/upload_injection.dart';

/// 全局依赖注入容器
final getIt = GetIt.instance;

/// 初始化依赖注入
///
/// 在应用启动时调用此函数，注册所有依赖
Future<void> setupInjection() async {
  // 注册核心依赖
  await setupCoreDependencies();

  // 注册功能模块依赖
  await setupAuthDependencies();
  await setupSplashDependencies();
  // 初始化安全教育模块依赖
  initSafetyInjection();
  // 初始化实习模块依赖
  await setupInternshipDependencies();
  // 初始化文件上传模块依赖
  await setupUploadDependencies();
  // 这些将在后续步骤中添加
  // await setupReportDependencies();
  // await setupSettingsDependencies();
}

/// 初始化核心依赖
///
/// 注册核心服务，如网络客户端和本地存储
Future<void> setupCoreDependencies() async {
  // 本地存储
  final sharedPrefs = await SharedPreferences.getInstance();
  getIt.registerLazySingleton<LocalStorage>(
    () => LocalStorageImpl(sharedPrefs),
  );

  // 日志工具
  final logger = Logger();
  getIt.registerLazySingleton<Logger>(() => logger);

  // 初始化日志工具
  await logger.init(
    envConfig: DioClient().envConfig,
    enableFileLogging: !kDebugMode, // 在非调试模式下启用文件日志
  );

  // 网络客户端
  final dioClient = DioClient();
  getIt.registerLazySingleton<DioClient>(() => dioClient);

  // 添加认证拦截器
  dioClient.addAuthInterceptor(getIt<LocalStorage>());

  // 网络信息
  getIt.registerLazySingleton(() => InternetConnectionChecker());
  getIt.registerLazySingleton<NetworkInfo>(
    () => NetworkInfoImpl(getIt<InternetConnectionChecker>()),
  );

  // 认证失效服务
  getIt.registerLazySingleton<AuthExpiryService>(
    () => AuthExpiryService(getIt<LocalStorage>()),
  );

  // 其他核心服务...
}

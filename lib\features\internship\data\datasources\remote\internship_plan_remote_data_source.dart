/// -----
/// internship_plan_remote_data_source.dart
/// 
/// 实习计划远程数据源接口定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../../data/models/internship_plan_model.dart';

/// 实习计划远程数据源接口
///
/// 定义从远程服务器获取实习计划数据的方法
abstract class InternshipPlanRemoteDataSource {
  /// 获取教师的实习计划列表
  ///
  /// 返回教师负责的所有实习计划
  Future<List<InternshipPlanModel>> getTeacherInternshipPlans();

  /// 获取实习计划详情
  ///
  /// [id] 实习计划ID
  /// 返回指定ID的实习计划详细信息
  Future<InternshipPlanModel> getInternshipPlanDetail(String id);
}

/// -----------------------------------------------------------------------------
/// course_header_section.dart
///
/// 可复用的课程头部组件，展示课程名称，支持展开/收起功能和课程选择
///
/// <AUTHOR>
/// @date 2025-04-10
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/course_selector_dialog.dart';

class CourseHeaderSection extends StatefulWidget {
  /// 当前选中的课程名称
  final String courseName;

  /// 初始是否展开
  final bool initialExpanded;

  /// 可选的课程列表，如果提供则显示课程选择功能
  final List<String>? availableCourses;

  /// 课程变更回调
  final Function(String newCourse)? onCourseChanged;

  const CourseHeaderSection({
    Key? key,
    required this.courseName,
    this.initialExpanded = false,
    this.availableCourses,
    this.onCourseChanged,
  }) : super(key: key);

  @override
  State<CourseHeaderSection> createState() => _CourseHeaderSectionState();
}

class _CourseHeaderSectionState extends State<CourseHeaderSection> {
  late bool _isExpanded;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initialExpanded;
  }

  /// 显示课程选择弹框
  void _showCourseSelector(BuildContext context) {
    if (widget.availableCourses == null || widget.availableCourses!.isEmpty) {
      return;
    }

    final int currentIndex = widget.availableCourses!.indexOf(widget.courseName);

    showCourseSelectorDialog(
      context: context,
      courses: widget.availableCourses!,
      selectedIndex: currentIndex >= 0 ? currentIndex : 0,
      onCourseSelected: (index, course) {
        if (widget.onCourseChanged != null) {
          widget.onCourseChanged!(course);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final bool hasCourseSelector = widget.availableCourses != null &&
                                  widget.availableCourses!.isNotEmpty &&
                                  widget.onCourseChanged != null;

    return Container(
      color: Colors.white,
      child: Column(
        children: [
          InkWell(
            onTap: hasCourseSelector
                ? () => _showCourseSelector(context)
                : () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.courseName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Icon(
                    hasCourseSelector
                        ? Icons.arrow_drop_down
                        : (_isExpanded ? Icons.expand_less : Icons.expand_more),
                    color: Colors.grey,
                  ),
                ],
              ),
            ),
          ),
          if (_isExpanded && !hasCourseSelector)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              child: Text(
                widget.courseName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
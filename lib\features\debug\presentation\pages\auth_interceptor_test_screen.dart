/// -----
/// auth_interceptor_test_screen.dart
/// 
/// 认证拦截器测试页面，用于验证401错误处理是否正常工作
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';

class AuthInterceptorTestScreen extends StatefulWidget {
  const AuthInterceptorTestScreen({Key? key}) : super(key: key);

  @override
  State<AuthInterceptorTestScreen> createState() => _AuthInterceptorTestScreenState();
}

class _AuthInterceptorTestScreenState extends State<AuthInterceptorTestScreen> {
  bool _isLoading = false;
  String _lastResult = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: const CustomAppBar(title: '认证拦截器测试'),
      body: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 说明文本
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Text(
                '此页面用于测试认证拦截器是否正常工作。\n'
                '点击下面的按钮会发送一个会返回401错误的请求，\n'
                '如果拦截器正常工作，应该会自动跳转到登录页面。',
                style: TextStyle(
                  fontSize: 28.sp,
                  color: Colors.blue.shade700,
                ),
              ),
            ),
            
            SizedBox(height: 32.h),
            
            // 测试按钮
            ElevatedButton(
              onPressed: _isLoading ? null : _testAuthInterceptor,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: _isLoading
                  ? SizedBox(
                      height: 20.h,
                      width: 20.w,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      '测试401错误处理',
                      style: TextStyle(fontSize: 32.sp),
                    ),
            ),
            
            SizedBox(height: 24.h),
            
            // 模拟业务状态码401错误的按钮
            ElevatedButton(
              onPressed: _isLoading ? null : _testBusinessCode401,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: _isLoading
                  ? SizedBox(
                      height: 20.h,
                      width: 20.w,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      '测试业务状态码10401',
                      style: TextStyle(fontSize: 32.sp),
                    ),
            ),
            
            SizedBox(height: 32.h),
            
            // 结果显示
            if (_lastResult.isNotEmpty)
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '最后一次测试结果:',
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      _lastResult,
                      style: TextStyle(fontSize: 26.sp),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 测试认证拦截器（发送会返回HTTP 401的请求）
  Future<void> _testAuthInterceptor() async {
    setState(() {
      _isLoading = true;
      _lastResult = '';
    });

    try {
      final dioClient = GetIt.instance<DioClient>();
      
      // 发送一个会返回401错误的请求
      await dioClient.get('/test/unauthorized');
      
      setState(() {
        _lastResult = '意外：请求成功了，这不应该发生';
      });
    } catch (e) {
      setState(() {
        _lastResult = '捕获到异常: $e\n'
                     '如果拦截器正常工作，应该已经跳转到登录页面';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试业务状态码401错误
  Future<void> _testBusinessCode401() async {
    setState(() {
      _isLoading = true;
      _lastResult = '';
    });

    try {
      final dioClient = GetIt.instance<DioClient>();
      
      // 发送一个正常的请求，但模拟服务器返回业务状态码10401
      await dioClient.get('internshipservice/v1/internship/student/file/require/list');
      
      setState(() {
        _lastResult = '请求成功，没有触发401错误';
      });
    } catch (e) {
      setState(() {
        _lastResult = '捕获到异常: $e\n'
                     '如果是认证相关错误，应该已经跳转到登录页面';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}

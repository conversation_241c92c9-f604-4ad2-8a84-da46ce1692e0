/// -----
/// file_upload_remote_data_source.dart
///
/// 文件上传远程数据源，处理与文件上传相关的API调用
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dio/dio.dart';
import '../../../../../core/error/exceptions/server_exception.dart';
import '../../../../../core/network/dio_client.dart';
import '../../models/file_requirement_model.dart';

/// 文件上传远程数据源接口
/// 
/// 定义与远程API交互的方法
abstract class FileUploadRemoteDataSource {
  /// 获取文件要求列表
  /// 
  /// [planId] 计划ID
  /// 返回：List<FileRequirementModel> 文件要求列表
  /// 抛出：ServerException 服务器异常
  Future<List<FileRequirementModel>> getFileRequirements({
    required String planId,
  });
}

/// 文件上传远程数据源实现
/// 
/// 实现与远程API交互的方法
class FileUploadRemoteDataSourceImpl implements FileUploadRemoteDataSource {
  FileUploadRemoteDataSourceImpl(this._dioClient);

  final DioClient _dioClient;

  @override
  Future<List<FileRequirementModel>> getFileRequirements({
    required String planId,
  }) async {
    try {
      final response = await _dioClient.get(
        'internshipservice/v1/internship/student/file/require/list',
        queryParameters: {
          'planId': planId,
        },
      );

      // DioClient的get方法已经提取了data字段，所以response直接是数据列表
      final responseData = response;

      // 检查响应数据是否为列表
      if (responseData is List) {
        final List<dynamic> dataList = responseData;

        final models = dataList
            .map((json) => FileRequirementModel.fromJson(json))
            .toList();

        return models;
      } else {
        return []; // 返回空列表而不是抛出异常
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('获取文件要求列表失败: $e');
    }
  }
}

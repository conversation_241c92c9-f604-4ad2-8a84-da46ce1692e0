/// -----
/// file_upload_event.dart
/// 
/// 文件上传BLoC事件定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 文件上传事件基类
abstract class FileUploadEvent extends Equatable {
  const FileUploadEvent();

  @override
  List<Object?> get props => [];
}

/// 加载文件要求列表事件
class LoadFileRequirementsEvent extends FileUploadEvent {
  /// 计划ID
  final String planId;

  const LoadFileRequirementsEvent({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];
}

/// 刷新文件要求列表事件
class RefreshFileRequirementsEvent extends FileUploadEvent {
  /// 计划ID
  final String planId;

  const RefreshFileRequirementsEvent({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];
}

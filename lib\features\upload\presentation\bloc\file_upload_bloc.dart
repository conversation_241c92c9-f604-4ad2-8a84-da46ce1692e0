/// -----
/// file_upload_bloc.dart
/// 
/// 文件上传BLoC，处理文件上传相关的状态管理
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/file_requirement.dart';
import '../../domain/usecases/get_file_requirements_usecase.dart';
import 'file_upload_event.dart';
import 'file_upload_state.dart';

/// 文件上传BLoC
/// 
/// 处理文件上传相关的状态管理
class FileUploadBloc extends Bloc<FileUploadEvent, FileUploadState> {
  FileUploadBloc(this._getFileRequirementsUseCase) : super(FileUploadInitial()) {
    on<LoadFileRequirementsEvent>(_onLoadFileRequirements);
    on<RefreshFileRequirementsEvent>(_onRefreshFileRequirements);
  }

  final GetFileRequirementsUseCase _getFileRequirementsUseCase;

  /// 处理加载文件要求列表事件
  Future<void> _onLoadFileRequirements(
    LoadFileRequirementsEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    emit(const FileUploadLoading(isFirstFetch: true));

    final result = await _getFileRequirementsUseCase(
      GetFileRequirementsParams(planId: event.planId),
    );

    result.fold(
      (failure) => emit(FileUploadFailure(message: failure.message)),
      (fileRequirements) => emit(FileUploadSuccess(fileRequirements: fileRequirements)),
    );
  }

  /// 处理刷新文件要求列表事件
  Future<void> _onRefreshFileRequirements(
    RefreshFileRequirementsEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    final currentState = state;
    List<FileRequirement> oldFileRequirements = [];

    if (currentState is FileUploadSuccess) {
      oldFileRequirements = currentState.fileRequirements;
    }

    emit(FileUploadLoading(oldFileRequirements: oldFileRequirements));

    final result = await _getFileRequirementsUseCase(
      GetFileRequirementsParams(planId: event.planId),
    );

    result.fold(
      (failure) => emit(FileUploadRefreshFailure(
        message: failure.message,
        fileRequirements: oldFileRequirements,
      )),
      (fileRequirements) => emit(FileUploadRefreshSuccess(
        fileRequirements: fileRequirements,
      )),
    );
  }
}

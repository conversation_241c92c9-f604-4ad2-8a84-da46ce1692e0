/// -----
/// free_internship_approval_list_screen.dart
///
/// 免实习申请审批列表页面，展示待审批和已审批的免实习申请
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/approval_tab_bar.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/approval_list_item.dart';
import 'package:flutter_demo/features/internship/data/models/free_internship_application_model.dart';
import 'package:flutter_demo/core/common/image_viewer_screen.dart';
import 'package:flutter_demo/features/internship/presentation/screens/free_internship_approval_detail_screen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FreeInternshipApprovalListScreen extends StatefulWidget {
  const FreeInternshipApprovalListScreen({Key? key}) : super(key: key);

  @override
  State<FreeInternshipApprovalListScreen> createState() =>
      _FreeInternshipApprovalListScreenState();
}

class _FreeInternshipApprovalListScreenState
    extends State<FreeInternshipApprovalListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _courseName = '2021级市场销售2023-2024实习学年第二学期岗位实习';

  // 可选的课程列表
  final List<String> _availableCourses = [
    '2023级市场销售2023-2024实习学年岗位实习',
    '2022级市场销售2023-2024实习学年岗位实习',
    '2021级市场销售2023-2024实习学年第二学期岗位实习',
    '2020级市场销售2023-2024实习学年岗位实习',
  ];

  // 分类后的申请列表
  late List<FreeInternshipApplicationModel> _pendingApplications;
  late List<FreeInternshipApplicationModel> _approvedApplications;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadData() {
    // 获取样例数据
    final List<FreeInternshipApplicationModel> allApplications =
        FreeInternshipApplicationModel.getSampleData();

    // 分类数据
    _pendingApplications =
        allApplications.where((app) => app.status == '待审批').toList();
    _approvedApplications =
        allApplications.where((app) => app.status != '待审批').toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: CustomAppBar(
        title: '免实习申请',
        centerTitle: true,
        showBackButton: true,
        actions: [
          IconButton(
            icon: Image.asset('assets/images/statistics_icon.png', width: 32.w, height: 32.h),
            onPressed: () {
              // TODO: 右侧图标点击事件
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // 课程头部 - 支持课程选择
          CourseHeaderSection(
            courseName: _courseName,
            initialExpanded: false,
            availableCourses: _availableCourses,
            onCourseChanged: (newCourse) {
              setState(() {
                _courseName = newCourse;
                _loadData();
              });
            },
          ),

          // 标签栏
          ApprovalTabBar(
            controller: _tabController,
            pendingCount: _pendingApplications.length,
          ),

          // 页面内容
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPendingList(),
                _buildApprovedList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 待审批列表
  Widget _buildPendingList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: _pendingApplications.length,
      itemBuilder: (context, index) {
        final item = _pendingApplications[index];
        return ApprovalListItem(
          item: item,
          isPending: true,
          onTap: () => _navigateToDetail(item),
          onViewTap: () => _navigateToDetail(item),
          contentBuilder: (item) => _buildFreeInternshipContent(item as FreeInternshipApplicationModel),
          attachmentBuilder: (item) => _buildAttachmentContent(item as FreeInternshipApplicationModel),
        );
      },
    );
  }

  // 已审批列表
  Widget _buildApprovedList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: _approvedApplications.length,
      itemBuilder: (context, index) {
        final item = _approvedApplications[index];
        return ApprovalListItem(
          item: item,
          isPending: false,
          onTap: () => _navigateToDetail(item),
          onViewTap: () => _navigateToDetail(item),
          contentBuilder: (item) => _buildFreeInternshipContent(item as FreeInternshipApplicationModel),
          attachmentBuilder: (item) => _buildAttachmentContent(item as FreeInternshipApplicationModel),
        );
      },
    );
  }

  // 构建免实习申请内容
  Widget _buildFreeInternshipContent(FreeInternshipApplicationModel item) {
    return buildInfoRow('学生去向', item.studentDestination);
  }

  // 构建附件内容
  Widget _buildAttachmentContent(FreeInternshipApplicationModel item) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '证明文件',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: item.attachments.isNotEmpty
              ? Align(
                  alignment: Alignment.centerLeft,
                  child: InkWell(
                    onTap: () => _viewAttachment(item.attachments.first),
                    child: Container(
                      width: 98,
                      height: 98,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!, width: 1),
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: Image.asset(
                        'assets/images/certificate_sample.png',
                        width: 98,
                        height: 98,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          width: 98,
                          height: 98,
                          color: Colors.grey[200],
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.insert_drive_file,
                                  size: 36,
                                  color: Colors.grey,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  item.attachments.first.name,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: AppTheme.primaryColor,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                )
              : const Text(
                  '无',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
        ),
      ],
    );
  }

  // 跳转到详情页面
  void _navigateToDetail(FreeInternshipApplicationModel item) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FreeInternshipApprovalDetailScreen(
          approvalId: item.id,
          status: item.status,
        ),
      ),
    ).then((result) {
      // 如果返回结果为true，表示审批状态有变化，需要刷新列表
      if (result == true) {
        setState(() {
          _loadData();
        });
      }
    });
  }

  // 查看附件
  void _viewAttachment(AttachmentInfo attachment) {
    // 打开全屏图片查看器
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ImageViewerScreen(
          // 优先使用附件URL，如果为空则使用示例图片
          imageUrl: attachment.url.isNotEmpty
              ? attachment.url
              : 'https://pic1.zhimg.com/v2-a58fa2ab84be291418da2652805f8270_b.jpg',
          title: attachment.name,
          isNetworkImage: true,
        ),
      ),
    );
  }
}